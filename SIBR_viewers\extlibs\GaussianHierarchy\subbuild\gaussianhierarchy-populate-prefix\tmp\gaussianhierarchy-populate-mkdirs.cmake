# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION 3.5)

file(MAKE_DIRECTORY
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy"
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/build"
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix"
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/tmp"
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp"
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src"
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp"
)

set(configSubDirs Debug)
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp${cfgdir}") # cfgdir has leading slash
endif()

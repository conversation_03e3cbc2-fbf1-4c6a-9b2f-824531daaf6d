﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0DB24CAA-90CC-3EF2-8407-638158AD12CB}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SoftWare\camke\bin\cmake.exe -SD:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild -BD:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild --check-stamp-file D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeDetermineSystem.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystem.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\RepositoryInfo.txt.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\cfgcmd.txt.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\gitclone.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\gitupdate.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\mkdirs.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\3.25.2\CMakeSystem.cmake;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\tmp\gaussianhierarchy-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\ZERO_CHECK.vcxproj">
      <Project>{C5C7015C-CA22-3010-814E-F744ABB482A4}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate.vcxproj">
      <Project>{46432ABC-6A23-3579-974C-106193553DB2}</Project>
      <Name>gaussianhierarchy-populate</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
# This is a generated file and its contents are an internal implementation detail.
# The download step will be re-executed if anything in this file changes.
# No other meaning or use of this file is supported.

method=git
command=D:/SoftWare/camke/bin/cmake.exe;-P;D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/tmp/gaussianhierarchy-populate-gitclone.cmake
source_dir=D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy
work_dir=D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy
repository=https://github.com/graphdeco-inria/gaussian-hierarchy.git
remote=origin
init_submodules=TRUE
recurse_submodules=--recursive
submodules=
CMP0097=NEW


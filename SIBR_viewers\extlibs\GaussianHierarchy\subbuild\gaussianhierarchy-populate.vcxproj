﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{46432ABC-6A23-3579-974C-106193553DB2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>gaussianhierarchy-populate</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-mkdir.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Creating directories for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SoftWare\camke\bin\cmake.exe -Dcfgdir=/Debug -P D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/tmp/gaussianhierarchy-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing download step (git clone) for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -P D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/tmp/gaussianhierarchy-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\gaussianhierarchy-populate-gitinfo.txt;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-update.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing update step for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\GaussianHierarchy
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -P D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/tmp/gaussianhierarchy-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-patch.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No patch step for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SoftWare\camke\bin\cmake.exe -E echo_append
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-configure.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No configure step for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E echo_append
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\tmp\gaussianhierarchy-populate-cfgcmd.txt;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-build.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No build step for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E echo_append
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-install.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No install step for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E echo_append
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-test.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No test step for 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E echo_append
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\fd8a11d4b0781239c003f78cf8044658\gaussianhierarchy-populate-complete.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Completed 'gaussianhierarchy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SoftWare\camke\bin\cmake.exe -E make_directory D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/CMakeFiles/Debug/gaussianhierarchy-populate-complete
if %errorlevel% neq 0 goto :cmEnd
D:\SoftWare\camke\bin\cmake.exe -E touch D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/Debug/gaussianhierarchy-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-install;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-mkdir;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-download;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-update;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-patch;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-configure;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-build;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\src\gaussianhierarchy-populate-stamp\Debug\gaussianhierarchy-populate-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\Debug\gaussianhierarchy-populate-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\98e4ecd3a21deed2c9920eda9b1698b3\gaussianhierarchy-populate.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\Debug\gaussianhierarchy-populate-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\gaussianhierarchy-populate</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SoftWare\camke\bin\cmake.exe -SD:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild -BD:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild --check-stamp-file D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeDetermineSystem.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystem.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\RepositoryInfo.txt.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\cfgcmd.txt.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\gitclone.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\gitupdate.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\mkdirs.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\3.25.2\CMakeSystem.cmake;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\tmp\gaussianhierarchy-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\gaussianhierarchy-populate">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\ZERO_CHECK.vcxproj">
      <Project>{C5C7015C-CA22-3010-814E-F744ABB482A4}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
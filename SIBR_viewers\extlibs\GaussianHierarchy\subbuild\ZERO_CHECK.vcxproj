﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C5C7015C-CA22-3010-814E-F744ABB482A4}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\98e4ecd3a21deed2c9920eda9b1698b3\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\SoftWare\camke\bin\cmake.exe -SD:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild -BD:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeDetermineSystem.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystem.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\RepositoryInfo.txt.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\cfgcmd.txt.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\gitclone.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\gitupdate.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\ExternalProject\mkdirs.cmake.in;D:\SoftWare\camke\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\SoftWare\camke\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\3.25.2\CMakeSystem.cmake;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeLists.txt;D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\gaussianhierarchy-populate-prefix\tmp\gaussianhierarchy-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
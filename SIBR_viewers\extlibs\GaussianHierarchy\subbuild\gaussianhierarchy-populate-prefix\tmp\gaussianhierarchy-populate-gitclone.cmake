# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION 3.5)

if(EXISTS "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitclone-lastrun.txt" AND EXISTS "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitinfo.txt" AND
  "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitclone-lastrun.txt" IS_NEWER_THAN "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitinfo.txt")
  message(STATUS
    "Avoiding repeated git clone, stamp file is up to date: "
    "'D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitclone-lastrun.txt'"
  )
  return()
endif()

execute_process(
  COMMAND ${CMAKE_COMMAND} -E rm -rf "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy"
  RESULT_VARIABLE error_code
)
if(error_code)
  message(FATAL_ERROR "Failed to remove directory: 'D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy'")
endif()

# try the clone 3 times in case there is an odd git clone issue
set(error_code 1)
set(number_of_tries 0)
while(error_code AND number_of_tries LESS 3)
  execute_process(
    COMMAND "D:/SoftWare/Git/cmd/git.exe" 
            clone --config "advice.detachedHead=false" "https://github.com/graphdeco-inria/gaussian-hierarchy.git" "GaussianHierarchy"
    WORKING_DIRECTORY "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy"
    RESULT_VARIABLE error_code
  )
  math(EXPR number_of_tries "${number_of_tries} + 1")
endwhile()
if(number_of_tries GREATER 1)
  message(STATUS "Had to git clone more than once: ${number_of_tries} times.")
endif()
if(error_code)
  message(FATAL_ERROR "Failed to clone repository: 'https://github.com/graphdeco-inria/gaussian-hierarchy.git'")
endif()

execute_process(
  COMMAND "D:/SoftWare/Git/cmd/git.exe" 
          checkout "677c8553dc64dfd62c272eca94a291a277733113" --
  WORKING_DIRECTORY "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy"
  RESULT_VARIABLE error_code
)
if(error_code)
  message(FATAL_ERROR "Failed to checkout tag: '677c8553dc64dfd62c272eca94a291a277733113'")
endif()

set(init_submodules TRUE)
if(init_submodules)
  execute_process(
    COMMAND "D:/SoftWare/Git/cmd/git.exe" 
            submodule update --recursive --init 
    WORKING_DIRECTORY "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy"
    RESULT_VARIABLE error_code
  )
endif()
if(error_code)
  message(FATAL_ERROR "Failed to update submodules in: 'D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy'")
endif()

# Complete success, update the script-last-run stamp file:
#
execute_process(
  COMMAND ${CMAKE_COMMAND} -E copy "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitinfo.txt" "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitclone-lastrun.txt"
  RESULT_VARIABLE error_code
)
if(error_code)
  message(FATAL_ERROR "Failed to copy script-last-run stamp file: 'D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/subbuild/gaussianhierarchy-populate-prefix/src/gaussianhierarchy-populate-stamp/gaussianhierarchy-populate-gitclone-lastrun.txt'")
endif()

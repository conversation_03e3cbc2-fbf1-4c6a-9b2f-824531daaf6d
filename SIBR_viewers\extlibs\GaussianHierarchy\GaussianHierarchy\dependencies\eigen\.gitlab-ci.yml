# This file is part of Eigen, a lightweight C++ template library
# for linear algebra.
#
# Copyright (C) 2023, The Eigen Authors
#
# This Source Code Form is subject to the terms of the Mozilla
# Public License v. 2.0. If a copy of the MPL was not distributed
# with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

stages:
  - checkformat
  - build
  - test

variables:
  # CMake build directory.
  EIGEN_CI_BUILDDIR: .build
  # Specify the CMake build target.
  EIGEN_CI_BUILD_TARGET: ""
  # If a test regex is specified, that will be selected.
  # Otherwise, we will try a label if specified.
  EIGEN_CI_TEST_REGEX: ""
  EIGEN_CI_TEST_LABEL: ""

include:
  - "/ci/checkformat.gitlab-ci.yml"
  - "/ci/common.gitlab-ci.yml"
  - "/ci/build.linux.gitlab-ci.yml"
  - "/ci/build.windows.gitlab-ci.yml"
  - "/ci/test.linux.gitlab-ci.yml"
  - "/ci/test.windows.gitlab-ci.yml"

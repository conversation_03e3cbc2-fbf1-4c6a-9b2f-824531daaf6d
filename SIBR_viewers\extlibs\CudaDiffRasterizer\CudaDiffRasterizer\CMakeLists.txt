#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#

cmake_minimum_required(VERSION 3.16)

# Use legacy CUDA support
find_package(CUDA REQUIRED)

project(CudaDiffRast LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_CUDA_STANDARD 17)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")

# Set CUDA architecture flags
set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS};-gencode arch=compute_70,code=sm_70;-gencode arch=compute_75,code=sm_75;-gencode arch=compute_86,code=sm_86)

# Force use of dynamic runtime library to match the main project
set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS};-Xcompiler /MD)

# CUDA source files
set(CUDA_SOURCES
	cuda_rasterizer/backward.cu
	cuda_rasterizer/forward.cu
	cuda_rasterizer/rasterizer_impl.cu
)

# Header files
set(HEADER_FILES
	cuda_rasterizer/backward.h
	cuda_rasterizer/forward.h
	cuda_rasterizer/auxiliary.h
	cuda_rasterizer/rasterizer_impl.h
	cuda_rasterizer/rasterizer.h
)

# Compile CUDA sources
cuda_add_library(CudaDiffRasterizer ${CUDA_SOURCES} ${HEADER_FILES})

target_include_directories(CudaDiffRasterizer PRIVATE third_party/glm ${CUDA_INCLUDE_DIRS})

target_include_directories(CudaDiffRasterizer
                           INTERFACE
                            $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
                            $<INSTALL_INTERFACE:include>
                           )

install(TARGETS
        CudaDiffRasterizer
    EXPORT CudaDiffRasterizerTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
)
install(FILES cuda_rasterizer/rasterizer.h DESTINATION include)
install(EXPORT CudaDiffRasterizerTargets
  FILE CudaDiffRasterizerConfig.cmake
  DESTINATION ${CMAKE_INSTALL_PREFIX}/cmake
)

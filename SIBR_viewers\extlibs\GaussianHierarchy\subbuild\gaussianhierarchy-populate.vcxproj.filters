﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\e3ebe1b6089532f0dc40ca664f7bd0a5\gaussianhierarchy-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\fd8a11d4b0781239c003f78cf8044658\gaussianhierarchy-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\98e4ecd3a21deed2c9920eda9b1698b3\gaussianhierarchy-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\wanleqi\hierarchical-3d-gaussians\SIBR_viewers\extlibs\GaussianHierarchy\subbuild\CMakeFiles\gaussianhierarchy-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{01B46F5B-6170-3350-923E-DB8241A43531}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

cmake_minimum_required (VERSION 3.0)

# Use legacy CUDA support
find_package(CUDA REQUIRED)

project (GaussianHierarchy LANGUAGES CXX)

# Set CUDA architecture flags
set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS};-gencode arch=compute_70,code=sm_70;-gencode arch=compute_75,code=sm_75;-gencode arch=compute_86,code=sm_86)

# Force use of dynamic runtime library to match the main project
set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS};-Xcompiler /MD)

# CUDA source files
set(CUDA_SOURCES
 runtime_maintenance.cu
 runtime_switching.cu
)

# C++ source files
set(CPP_SOURCES
 FlatGenerator.cpp
 PointbasedKdTreeGenerator.cpp
 ClusterMerger.cpp
 appearance_filter.cpp
 AvgMerger.cpp
 writer.cpp
 loader.cpp
 hierarchy_loader.cpp
 hierarchy_explicit_loader.cpp
 hierarchy_writer.cpp
 traversal.cpp
 rotation_aligner.cpp
)

# Header files
set(HEADER_FILES
 FlatGenerator.h
 PointbasedKdTreeGenerator.h
 ClusterMerger.h
 appearance_filter.h
 AvgMerger.h
 writer.h
 common.h
 loader.h
 hierarchy_loader.h
 hierarchy_explicit_loader.h
 hierarchy_writer.h
 traversal.h
 runtime_maintenance.h
 runtime_switching.h
 rotation_aligner.h
 half.hpp
 types.h
)

# Compile CUDA sources and create library
cuda_add_library(GaussianHierarchy ${CUDA_SOURCES} ${CPP_SOURCES} ${HEADER_FILES})
target_include_directories(GaussianHierarchy PRIVATE dependencies/eigen ${CUDA_INCLUDE_DIRS})

set_property(TARGET GaussianHierarchy PROPERTY CXX_STANDARD 17)

target_include_directories(GaussianHierarchy
                           PUBLIC
                            $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
                            $<INSTALL_INTERFACE:include>
                           )
						   
install(TARGETS
        GaussianHierarchy
    EXPORT GaussianHierarchyTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
)
install(FILES runtime_maintenance.h runtime_switching.h hierarchy_loader.h types.h DESTINATION include)
install(EXPORT GaussianHierarchyTargets
  FILE GaussianHierarchyConfig.cmake
  DESTINATION ${CMAKE_INSTALL_PREFIX}/cmake
)

add_executable (GaussianHierarchyCreator
 mainHierarchyCreator.cpp
)

add_executable (GaussianHierarchyMerger
 mainHierarchyMerger.cpp
)

target_include_directories(GaussianHierarchyCreator PRIVATE dependencies/eigen)
set_property(TARGET GaussianHierarchyCreator PROPERTY CXX_STANDARD 17)
target_link_libraries(GaussianHierarchyCreator PUBLIC GaussianHierarchy)

target_include_directories(GaussianHierarchyMerger PRIVATE dependencies/eigen)
set_property(TARGET GaussianHierarchyMerger PROPERTY CXX_STANDARD 17)
target_link_libraries(GaussianHierarchyMerger PUBLIC GaussianHierarchy)
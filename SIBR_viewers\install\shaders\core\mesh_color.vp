/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON><PERSON>@inria.fr
 */


#version 420

uniform mat4 MVP;
uniform mat4 invMV;

layout(location = 0) in vec3 in_vertex;
layout(location = 1) in vec3 in_color;
layout(location = 3) in vec3 in_normal;

out vec3 color_vert;
out vec3 vertexPos; 
out vec3 normalPos;

void main(void) {
	gl_Position = MVP * vec4(in_vertex,1.0);
	vertexPos = vec3(MVP * vec4(in_vertex,1.0));
	normalPos = vec3(invMV*vec4(in_normal,1.0));
	
	color_vert = in_color;
}

# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION 3.25.2)

# We name the project and the target for the ExternalProject_Add() call
# to something that will highlight to the user what we are working on if
# something goes wrong and an error message is produced.

project(gaussianhierarchy-populate NONE)


# Pass through things we've already detected in the main project to avoid
# paying the cost of redetecting them again in ExternalProject_Add()
set(GIT_EXECUTABLE [==[D:/SoftWare/Git/cmd/git.exe]==])
set(GIT_VERSION_STRING [==[2.20.1.windows.1]==])
set_property(GLOBAL PROPERTY _CMAKE_FindGit_GIT_EXECUTABLE_VERSION
  [==[D:/SoftWare/Git/cmd/git.exe;2.20.1.windows.1]==]
)


include(ExternalProject)
ExternalProject_Add(gaussianhierarchy-populate
                     "GIT_REPOSITORY" "https://github.com/graphdeco-inria/gaussian-hierarchy.git" "GIT_TAG" "677c8553dc64dfd62c272eca94a291a277733113"
                    SOURCE_DIR          "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/GaussianHierarchy"
                    BINARY_DIR          "D:/wanleqi/hierarchical-3d-gaussians/SIBR_viewers/extlibs/GaussianHierarchy/build"
                    CONFIGURE_COMMAND   ""
                    BUILD_COMMAND       ""
                    INSTALL_COMMAND     ""
                    TEST_COMMAND        ""
                    USES_TERMINAL_DOWNLOAD  YES
                    USES_TERMINAL_UPDATE    YES
                    USES_TERMINAL_PATCH     YES
)


